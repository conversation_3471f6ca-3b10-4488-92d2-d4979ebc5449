import type { ServerInfo, SysUserInfo } from '@/types';
import Utils from './utils';
import type { ShallowRef } from 'vue';
import { ws } from '../api/websocket';

/**
 * 业务逻辑相关方法
 */
class Misc {
  /**
   * 保存用户信息
   */
  static setUser(user?: any) {
    Utils.setLocal('user', user);
  }

  /**
   * 获取用户信息
   */
  static getUser() {
    return Utils.getLocal<SysUserInfo>('user');
  }

  /**
   * 是否登录
   */
  static loggedIn() {
    return !!this.getUser();
  }

  /**
   * 保存服务器信息
   */
  static setServer(server: ServerInfo) {
    Utils.setLocal('server', server);
    this.connect();
  }

  static connect() {
    const server = this.getServer();
    if (server) {
      ws.connect(server.ws);
    }
  }

  /**
   * 获取服务器信息
   */
  static getServer() {
    return Utils.getLocal<ServerInfo>('server');
  }

  /**
   * 是否为web端
   */
  static isWeb() {
    return !this.isElectron();
  }

  /**
   * 是否为客户端
   */
  static isElectron() {
    // Renderer process
    if (
      typeof window !== 'undefined' &&
      typeof window.process === 'object' &&
      window.process.type === 'renderer'
    ) {
      return true;
    }

    // Main process
    if (
      typeof process !== 'undefined' &&
      typeof process.versions === 'object' &&
      !!process.versions.electron
    ) {
      return true;
    }

    // Detect the user agent when the `nodeIntegration` option is set to true
    if (
      typeof navigator === 'object' &&
      typeof navigator.userAgent === 'string' &&
      navigator.userAgent.indexOf('Electron') >= 0
    ) {
      return true;
    }

    return false;
  }

  /**
   * 更新或添加数据到数组中
   * @param item 要更新或添加的数据项
   * @param data 目标数组的浅引用
   * @param identity 用于标识唯一性的字段名，默认为'id'
   * @description 如果数组中已存在相同identity的项，则更新该项；否则将新项添加到数组末尾
   */
  static putRow<T extends Record<string, any>>(
    item: T,
    data: ShallowRef<T[]>,
    identity: keyof T = 'id',
  ) {
    if (data.value.some(row => row[identity] === item[identity])) {
      data.value = data.value.map(row => (row[identity] === item[identity] ? item : row));
    } else {
      data.value = [...data.value, item];
    }
  }
}

export default Misc;
