<script setup lang="tsx">
import { onMounted, shallowRef } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { TablePositionInfo, ColumnDefinition, PoolDetail } from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';
import { useStockSelectionStore } from '@/stores';
import { CaretLeft, CaretRight } from '@element-plus/icons-vue';

// 展开收起
const expanded = shallowRef(true);

// 响应式数据
const positions = shallowRef<TablePositionInfo[]>([]);
const { setSelectedStock } = useStockSelectionStore();

// 处理行点击事件
const handleRowClick = (row: TablePositionInfo) => {
  const stockInfo: PoolDetail = {
    id: 0,
    instrument: row.instrument,
    instrumentName: row.instrumentName,
    status: 1,
    risePercent: 0,
    poolName: '',
    poolId: 0,
    position: 0,
  };
  setSelectedStock(stockInfo);
};

// 持仓列表表格列定义
const positionColumns: ColumnDefinition<TablePositionInfo> = [
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 100,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '策略设置',
    width: 80,
    cellRenderer: () => {
      return <span c-blue>低封单量</span>;
    },
  },
  {
    key: 'floatProfit',
    dataKey: 'floatProfit',
    title: '涨跌幅',
    width: 80,
    cellRenderer: ({ rowData }) => {
      // 计算涨跌幅 (浮动盈亏 / 市值) * 100
      const risePercent =
        rowData.marketValue > 0 ? (rowData.floatProfit / rowData.marketValue) * 100 : 0;
      const color =
        risePercent > 0 ? 'c-[var(--g-red)]' : risePercent < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return (
        <span class={color}>{Utils.formatNumber(risePercent, { fix: 2, prefix: true })}%</span>
      );
    },
  },
  {
    key: 'todayPosition',
    dataKey: 'todayPosition',
    title: '持仓数量',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'yesterdayPosition',
    dataKey: 'yesterdayPosition',
    title: '可用数量',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 100,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'avgPrice',
    dataKey: 'avgPrice',
    title: '仓位',
    width: 60,
    cellRenderer: () => <span>5%</span>,
  },
  {
    key: 'floatProfit',
    dataKey: 'floatProfit',
    title: '盈亏额',
    width: 80,
    cellRenderer: ({ cellData }) => {
      let color =
        cellData > 0 ? 'c-[var(--g-red)]' : cellData < 0 ? 'c-[var(--g-green)]' : 'c-white';
      color += ' toe';
      return (
        <div
          title={Utils.formatNumber(cellData, { separator: true, prefix: true })}
          w-full
          class={color}
        >
          {Utils.formatNumber(cellData, { separator: true, prefix: true })}
        </div>
      );
    },
  },
  {
    key: 'closeProfit',
    dataKey: 'closeProfit',
    title: '盈亏比',
    width: 70,
    cellRenderer: ({ rowData }) => {
      // 计算盈亏比 (浮动盈亏 / 市值) * 100
      const profitRatio =
        rowData.marketValue > 0 ? (rowData.floatProfit / rowData.marketValue) * 100 : 0;
      const color =
        profitRatio > 0 ? 'c-[var(--g-red)]' : profitRatio < 0 ? 'c-[var(--g-green)]' : 'c-white';
      return <span class={color}>{Utils.formatNumber(profitRatio, { fix: 2 })}%</span>;
    },
  },
];

// 获取持仓列表
const fetchPositions = async () => {
  const { errorCode, data } = await RecordService.getPositions(37294719234);
  if (errorCode === 0 && data) {
    positions.value = data;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchPositions();
});

const toggle = () => {
  expanded.value = !expanded.value;
};
defineExpose({
  expanded,
});
</script>

<template>
  <div pos-relative flex="~ col" h-full>
    <!-- 持仓标题 -->
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>持仓</span>
    </div>

    <!-- 持仓列表 -->
    <div flex-1 min-h-1>
      <VirtualizedTable
        identity="instrument"
        :columns="positionColumns"
        :data="positions"
        @row-click="handleRowClick"
      />
    </div>
    <!-- 折叠按钮 -->
    <div
      :class="expanded ? 'left-0' : 'left--8'"
      top="50%"
      pos-absolute
      w-12
      h-24
      bg="[--g-panel-bg3-l]"
      cursor-pointer
      flex
      aic
      jcc
      rounded-2
      z-2
      hover="bg-[--g-bg-hover]"
      @click="toggle"
    >
      <el-icon v-if="expanded"><CaretRight /></el-icon>
      <el-icon v-else><CaretLeft /></el-icon>
    </div>
  </div>
</template>

<style scoped></style>
