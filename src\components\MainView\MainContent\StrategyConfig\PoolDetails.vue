<script setup lang="tsx">
import type { ColumnDefinition, InstrumentInfo, PoolDetail, RowAction } from '@/types';
import { ref, shallowRef, watch } from 'vue';
import { Utils } from '@/script';
import { PoolService } from '@/api';
import { usePoolSelectionStore } from '@/stores';
import { storeToRefs } from 'pinia';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { AssetTypeEnum, InstrumentStatusEnum } from '@/enum';
import { ElMessage, ElMessageBox } from 'element-plus';
import InstrumentInput from '@/components/common/InstrumentInput.vue';

// 表格列定义
const columns: ColumnDefinition<PoolDetail> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'position',
    dataKey: 'position',
    title: '仓位',
    width: 200,
    cellRenderer: ({ cellData }) => {
      return <span>{Utils.formatNumber(cellData, { percent: true })}</span>;
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const text = InstrumentStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
];

const rowActions: RowAction<PoolDetail>[] = [
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { errorCode, errorMsg } = await PoolService.deletePoolDetail(row.id);
        if (errorCode === 0) {
          ElMessage.success('删除成功');
          details.value = details.value.filter(x => x.id !== row.id);
        } else {
          ElMessage.error(errorMsg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const details = shallowRef<PoolDetail[]>([]);
const { selectedPool } = storeToRefs(usePoolSelectionStore());
const selectedInstrument = ref<InstrumentInfo>();
const positionRate = ref(0);

// 获取股票池详情数据
const getDetails = async (poolId: number) => {
  const { errorCode, data } = await PoolService.getPoolDetail(poolId);
  if (errorCode === 0 && data) {
    details.value = data;
  } else {
    details.value = [];
  }
};

// 监听选中股票池变化
watch(
  selectedPool,
  newPool => {
    if (newPool) {
      getDetails(newPool.id);
    } else {
      details.value = [];
    }
  },
  { immediate: true },
);

const handleImport = () => {};
const handleClear = () => {};
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 jcsb>
      <div flex aic gap-10>
        <div text-14 font-bold>股票池详情</div>
        <InstrumentInput
          flex-1
          min-w-1
          v-model="selectedInstrument"
          :assetType="AssetTypeEnum.股票"
          placeholder="请输入合约代码或名称"
          w-full
        />
        <div flex aic gap-10>
          <div>股票池仓位</div>
          <el-input v-model="positionRate" class="position-input">
            <template #append>%</template>
          </el-input>
        </div>
      </div>
      <div>
        <el-button color="var(--g-primary)" @click="handleImport">
          <i mr-4 fs-14 i-mdi-import />
          导入
        </el-button>
        <el-button color="var(--g-red)" @click="handleClear">
          <i mr-4 fs-14 i-mdi-trash-can-outline />
          清空
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      showIndex
      :data="details"
      :columns="columns"
      :row-actions="rowActions"
    ></VirtualizedTable>
  </div>
</template>

<style scoped>
.position-input {
  width: 70px;
}
:deep() {
  .el-input-group__append {
    padding: 0 5px;
  }
}
</style>
