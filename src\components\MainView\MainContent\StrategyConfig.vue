<script setup lang="ts">
import InstrumentPool from './StrategyConfig/InstrumentPool.vue';
import PoolDetails from './StrategyConfig/PoolDetails.vue';
import PoolConfig from './StrategyConfig/PoolConfig.vue';
</script>

<template>
  <div overflow-hidden flex p-10 gap-10>
    <div flex="~ col" gap-10 w="50%">
      <InstrumentPool h="50%" bg="[--g-panel-bg]" />
      <PoolDetails flex-1 min-h-1 bg="[--g-panel-bg]" />
    </div>
    <PoolConfig min-w-1 flex-1 bg="[--g-panel-bg]" />
  </div>
</template>

<style scoped></style>
