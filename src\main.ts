import './assets/style/main.css';
import 'virtual:uno.css';
import 'element-plus/dist/index.css';
import 'element-plus/theme-chalk/dark/css-vars.css';
import { createApp } from 'vue';
import { createPinia } from 'pinia';
import ElementPlus from 'element-plus';
import zhCn from 'element-plus/es/locale/lang/zh-cn';

import App from './App.vue';
import router from './router';
import { Misc } from './script';

const app = createApp(App);

app.use(ElementPlus, {
  locale: zhCn,
  size: 'small',
});
app.use(createPinia());
app.use(router);

app.mount('#app');

Misc.connect();
