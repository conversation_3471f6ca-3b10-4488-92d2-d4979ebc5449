<script setup lang="ts">
import { ref, shallowRef, computed, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { usePoolSelectionStore } from '@/stores';
import { PoolService } from '@/api';
import { ElMessage } from 'element-plus';
import { PoolStatusEnum } from '@/enum';
import type {
  Pool,
  StrategyConfig,
  CancelConfig,
  CancelConfig1,
  CancelConfig2,
  CancelConfig3,
  EffectTime,
} from '@/types';

const { selectedPool } = storeToRefs(usePoolSelectionStore());

// 当前激活的tab
const activeTab = ref('buyTrigger');

// 原始配置数据（用于取消时还原）
const originalConfig = shallowRef<Pool | null>(null);

// 表单数据
const formData = ref({
  // 买入触发配置
  strategyConfig: {
    limitTriggerNum: 3,
    shConfig: [
      { firstAmount: 1, secondAmount: 2000, positionEffect: 20 },
      { firstAmount: 300, secondAmount: 500, positionEffect: 30 },
      { firstAmount: 0, secondAmount: 0, positionEffect: 15 },
    ],
    szConfig: [
      { firstAmount: 1, secondAmount: 2000, positionEffect: 20 },
      { firstAmount: 300, secondAmount: 500, positionEffect: 30 },
      { firstAmount: 0, secondAmount: 0, positionEffect: 15 },
    ],
  } as StrategyConfig,
  // 撤单配置 - 使用具体的类型结构
  cancelConfig: {
    config1: {
      cancelType: 1 as const,
      cancelInfo: { tradeTime: 200 },
    } as CancelConfig1,
    config2: {
      cancelType: 2 as const,
      cancelInfo: { orderTime: 200, frontAmount: 200, afterAmount: 8000 },
    } as CancelConfig2,
    config3: {
      cancelType: 3 as const,
      cancelInfo: { volume: 20000, time: 200, downRate: 20 },
    } as CancelConfig3,
  },
  // 运行时间配置
  effectTime: {
    am: { begin: '09:30:00', end: '11:30:00' },
    pm: { begin: '13:00:00', end: '14:57:00' },
  } as EffectTime,
});

// 是否禁用表单（运行中时禁用）
const isDisabled = computed(() => {
  return selectedPool.value?.status === PoolStatusEnum.运行中;
});

// 监听选中股票池变化
watch(
  selectedPool,
  newPool => {
    if (newPool) {
      // 保存原始配置
      originalConfig.value = JSON.parse(JSON.stringify(newPool));
      // 更新表单数据
      formData.value.strategyConfig = { ...newPool.strategyConfig };
      // 转换撤单配置数组为对象结构
      const cancelConfigs = newPool.cancelConfig;
      formData.value.cancelConfig.config1 =
        (cancelConfigs.find(c => c.cancelType === 1) as CancelConfig1) ||
        formData.value.cancelConfig.config1;
      formData.value.cancelConfig.config2 =
        (cancelConfigs.find(c => c.cancelType === 2) as CancelConfig2) ||
        formData.value.cancelConfig.config2;
      formData.value.cancelConfig.config3 =
        (cancelConfigs.find(c => c.cancelType === 3) as CancelConfig3) ||
        formData.value.cancelConfig.config3;
      formData.value.effectTime = { ...newPool.effectTime };
    } else {
      originalConfig.value = null;
    }
  },
  { immediate: true },
);

// 保存配置
const handleSave = async () => {
  if (!selectedPool.value) return;

  try {
    // 将对象结构转换为数组结构
    const cancelConfigArray = [
      formData.value.cancelConfig.config1,
      formData.value.cancelConfig.config2,
      formData.value.cancelConfig.config3,
    ];

    const { errorCode, errorMsg } = await PoolService.updatePoolConfig(selectedPool.value.id, {
      strategyConfig: formData.value.strategyConfig,
      cancelConfig: cancelConfigArray,
      effectTime: formData.value.effectTime,
    });

    if (errorCode === 0) {
      ElMessage.success('配置保存成功');
      // 更新原始配置
      originalConfig.value = JSON.parse(
        JSON.stringify({
          ...selectedPool.value,
          strategyConfig: formData.value.strategyConfig,
          cancelConfig: cancelConfigArray,
          effectTime: formData.value.effectTime,
        }),
      );
    } else {
      ElMessage.error(errorMsg || '保存失败');
    }
  } catch (error) {
    ElMessage.error('保存失败');
  }
};

// 取消配置（还原为服务端配置）
const handleCancel = () => {
  if (originalConfig.value) {
    formData.value.strategyConfig = { ...originalConfig.value.strategyConfig };
    // 转换撤单配置数组为对象结构
    const cancelConfigs = originalConfig.value.cancelConfig;
    formData.value.cancelConfig.config1 =
      (cancelConfigs.find(c => c.cancelType === 1) as CancelConfig1) ||
      formData.value.cancelConfig.config1;
    formData.value.cancelConfig.config2 =
      (cancelConfigs.find(c => c.cancelType === 2) as CancelConfig2) ||
      formData.value.cancelConfig.config2;
    formData.value.cancelConfig.config3 =
      (cancelConfigs.find(c => c.cancelType === 3) as CancelConfig3) ||
      formData.value.cancelConfig.config3;
    formData.value.effectTime = { ...originalConfig.value.effectTime };
    ElMessage.info('已还原为服务端配置');
  }
};
</script>

<template>
  <div flex="~ col" h-full>
    <div h-32 flex aic px-16 jcsb>
      <div text-14 font-bold>股票池配置</div>
      <div v-if="selectedPool">
        <el-button :disabled="isDisabled" @click="handleCancel">取消</el-button>
        <el-button :disabled="isDisabled" type="primary" @click="handleSave">保存</el-button>
      </div>
    </div>

    <div v-if="!selectedPool" flex-1 flex aic jcc text-gray-400>请选择股票池</div>

    <div v-else flex="~ col" flex-1 min-h-1 px-16 pb-16>
      <el-tabs v-model="activeTab" class="pool-config-tabs">
        <el-tab-pane label="买入触发" name="buyTrigger">
          <div class="config-content">
            <el-form :disabled="isDisabled" label-width="120px">
              <el-form-item label="涨停排名前N名">
                <el-input-number
                  v-model="formData.strategyConfig.limitTriggerNum"
                  :min="1"
                  :max="100"
                  :step="1"
                  style="width: 200px"
                />
                <span ml-8>只</span>
              </el-form-item>
            </el-form>

            <!-- 沪市配置 -->
            <div mt-24>
              <div text-14 font-bold mb-12>沪市</div>
              <el-table :data="formData.strategyConfig.shConfig" border>
                <el-table-column label="" width="80" align="center">
                  <template #default="{ $index }">
                    <span>条件{{ $index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="首次排单金额(万)" width="180" align="center">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.firstAmount"
                      :disabled="isDisabled"
                      :min="0"
                      :step="100"
                      size="small"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="二次排单金额(万)" width="180" align="center">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.secondAmount"
                      :disabled="isDisabled"
                      :min="0"
                      :step="100"
                      size="small"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="仓位比例" align="center">
                  <template #default="{ row }">
                    <div flex aic>
                      <el-input-number
                        v-model="row.positionEffect"
                        :disabled="isDisabled"
                        :min="0"
                        :max="100"
                        :step="1"
                        size="small"
                        style="width: 100px"
                      />
                      <span ml-8>%</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 深市配置 -->
            <div mt-24>
              <div text-14 font-bold mb-12>深市</div>
              <el-table :data="formData.strategyConfig.szConfig" border>
                <el-table-column label="" width="80" align="center">
                  <template #default="{ $index }">
                    <span>条件{{ $index + 1 }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="首次排单金额(万)" width="180" align="center">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.firstAmount"
                      :disabled="isDisabled"
                      :min="0"
                      :step="100"
                      size="small"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="二次排单金额(万)" width="180" align="center">
                  <template #default="{ row }">
                    <el-input-number
                      v-model="row.secondAmount"
                      :disabled="isDisabled"
                      :min="0"
                      :step="100"
                      size="small"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="仓位比例" align="center">
                  <template #default="{ row }">
                    <div flex aic>
                      <el-input-number
                        v-model="row.positionEffect"
                        :disabled="isDisabled"
                        :min="0"
                        :max="100"
                        :step="1"
                        size="small"
                        style="width: 100px"
                      />
                      <span ml-8>%</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="撤单" name="cancel">
          <div class="config-content">
            <el-form :disabled="isDisabled" label-width="120px">
              <!-- 撤单类型1：当一笔成交时间小于N秒，则撤单 -->
              <div class="cancel-config-item">
                <div text-14 font-bold mb-12>撤单1</div>
                <el-form-item label="当一笔成交时间小于">
                  <div flex aic>
                    <el-input-number
                      v-model="formData.cancelConfig.config1.cancelInfo.tradeTime"
                      :min="1"
                      :step="10"
                      style="width: 150px"
                    />
                    <span ml-8>秒，则撤单</span>
                  </div>
                </el-form-item>
              </div>

              <!-- 撤单类型2：下单时间、前少、后多 -->
              <div class="cancel-config-item">
                <div text-14 font-bold mb-12>撤单2</div>
                <el-form-item label="下单">
                  <div flex aic>
                    <el-input-number
                      v-model="formData.cancelConfig.config2.cancelInfo.orderTime"
                      :min="1"
                      :step="10"
                      style="width: 100px"
                    />
                    <span mx-8>秒之后，前少</span>
                    <el-input-number
                      v-model="formData.cancelConfig.config2.cancelInfo.frontAmount"
                      :min="0"
                      :step="100"
                      style="width: 100px"
                    />
                    <span mx-8>万元，后多</span>
                    <el-input-number
                      v-model="formData.cancelConfig.config2.cancelInfo.afterAmount"
                      :min="0"
                      :step="100"
                      style="width: 100px"
                    />
                    <span ml-8>万元，则撤单</span>
                  </div>
                </el-form-item>
              </div>

              <!-- 撤单类型3：封单手数、时间、回落比例 -->
              <div class="cancel-config-item">
                <div text-14 font-bold mb-12>撤单3</div>
                <el-form-item label="封单手数低于">
                  <div flex aic>
                    <el-input-number
                      v-model="formData.cancelConfig.config3.cancelInfo.volume"
                      :min="0"
                      :step="1000"
                      style="width: 120px"
                    />
                    <span mx-8>手</span>
                    <el-input-number
                      v-model="formData.cancelConfig.config3.cancelInfo.time"
                      :min="1"
                      :step="10"
                      style="width: 100px"
                    />
                    <span mx-8>秒，回落比例超过</span>
                    <el-input-number
                      v-model="formData.cancelConfig.config3.cancelInfo.downRate"
                      :min="0"
                      :max="100"
                      :step="1"
                      style="width: 100px"
                    />
                    <span ml-8>%，则撤单</span>
                  </div>
                </el-form-item>
              </div>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="运行时间" name="runtime">
          <div class="config-content">
            <el-form :disabled="isDisabled" label-width="150px">
              <!-- 股票池监控开始时间1 -->
              <el-form-item label="股票池监控开始时间1">
                <el-time-picker
                  v-model="formData.effectTime.am.begin"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  placeholder="选择时间"
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 股票池监控结束时间1 -->
              <el-form-item label="股票池监控结束时间1">
                <el-time-picker
                  v-model="formData.effectTime.am.end"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  placeholder="选择时间"
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 股票池监控开始时间2 -->
              <el-form-item label="股票池监控开始时间2">
                <el-time-picker
                  v-model="formData.effectTime.pm.begin"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  placeholder="选择时间"
                  style="width: 200px"
                />
              </el-form-item>

              <!-- 股票池监控结束时间2 -->
              <el-form-item label="股票池监控结束时间2">
                <el-time-picker
                  v-model="formData.effectTime.pm.end"
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  placeholder="选择时间"
                  style="width: 200px"
                />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style scoped>
.pool-config-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.pool-config-tabs :deep(.el-tabs__content) {
  flex: 1;
  min-height: 1px;
  overflow: auto;
}

.config-content {
  height: 100%;
  padding: 16px 0;
}

.cancel-config-item {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  background-color: var(--el-bg-color-page);
}
</style>
