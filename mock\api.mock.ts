import { defineMock } from 'vite-plugin-mock-dev-server';
import {
  PoolDetail,
  TableOrderInfo,
  TablePositionInfo,
  TableTradeRecordInfo,
  AccountInfo,
  StandardTick,
  RealtimeTrade,
  StrategyLog,
  Pool,
} from '../src/types';
import {
  PositionDirectionEnum,
  TradeDirectionEnum,
  OrderStatusEnum,
  PositionEffectEnum,
} from '../src/enum';

// 模拟账号数据
const mockAccounts: AccountInfo[] = [
  {
    id: 1,
    accountId: ***********,
    accountName: '爱建测试1901',
    financeAccount: 'n31806',
    assetType: 1,
    available: ********.59,
    balance: 1********.57,
    buyMarketValue: ********.32,
    closeProfit: 90.38,
    commission: 12975.47,
    connectionStatus: true,
    credit: false,
    dayProfit: 81276.23,
    fundId: 1,
    fundName: '基金A',
    fundShare: 1.0,
    frozenMargin: 0,
    frozenCommission: 0,
    inMoney: 0,
    loanBuyBalance: 0,
    loanSellBalance: 0,
    loanSellQuota: 0,
    marketValue: ********.05,
    margin: 0,
    maxLimitMoney: ********.59,
    nav: 1.0238,
    outMoney: 0,
    positionProfit: 0,
    preBalance: ********.57,
    risePercent: 0.0992,
    sellMarketValue: 0,
    stockIndexMarketValue: ********.05,
    tradingDay: '2023-01-01',
    enableCreditBuy: 0,
  },
  {
    id: 2,
    accountId: ***********,
    accountName: '兆富机构',
    financeAccount: 'n31807',
    assetType: 1,
    available: ********.89,
    balance: ********.23,
    buyMarketValue: ********.9,
    closeProfit: 85.67,
    commission: 8765.43,
    connectionStatus: true,
    credit: false,
    dayProfit: -2304.12,
    fundId: 2,
    fundName: '基金B',
    fundShare: 1.0,
    frozenMargin: 0,
    frozenCommission: 0,
    inMoney: 0,
    loanBuyBalance: 0,
    loanSellBalance: 0,
    loanSellQuota: 0,
    marketValue: ********.23,
    margin: 0,
    maxLimitMoney: ********.89,
    nav: 1.0785,
    outMoney: 0,
    positionProfit: 0,
    preBalance: ********.23,
    risePercent: 0.0785,
    sellMarketValue: 0,
    stockIndexMarketValue: ********.23,
    tradingDay: '2023-01-01',
    enableCreditBuy: 0,
  },
  {
    id: 3,
    accountId: ***********,
    accountName: '平安银行',
    financeAccount: 'n31808',
    assetType: 1,
    available: 8765432.1,
    balance: ********9.01,
    buyMarketValue: 9876543.21,
    closeProfit: 75.43,
    commission: 5432.1,
    connectionStatus: true,
    credit: false,
    dayProfit: 4123412.34,
    fundId: 3,
    fundName: '基金C',
    fundShare: 1.0,
    frozenMargin: 0,
    frozenCommission: 0,
    inMoney: 0,
    loanBuyBalance: 0,
    loanSellBalance: 0,
    loanSellQuota: 0,
    marketValue: ********.01,
    margin: 0,
    maxLimitMoney: 8765432.1,
    nav: 1.0543,
    outMoney: 0,
    positionProfit: 0,
    preBalance: ********.01,
    risePercent: 0.0543,
    sellMarketValue: 0,
    stockIndexMarketValue: ********.01,
    tradingDay: '2023-01-01',
    enableCreditBuy: 0,
  },
];

// 模拟持仓数据
const mockPositions: TablePositionInfo[] = [
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '600036',
    instrumentName: '招商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -91248.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '600519',
    instrumentName: '贵州茅台',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -32862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601398',
    instrumentName: '工商银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: 123948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601288',
    instrumentName: '农业银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: 4444234.72,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601988',
    instrumentName: '中国银行',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: 3456345.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.多头,
    instrument: '601628',
    instrumentName: '中国人寿',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -2222948.43,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
  {
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    direction: PositionDirectionEnum.空头,
    instrument: '601318',
    instrumentName: '中国平安',
    yesterdayPosition: 100,
    todayPosition: 100,
    closeProfit: 0,
    floatProfit: -1232862.84,
    avgPrice: 31.32,
    lastSettlePrice: 31.01,
    usedCommission: 40,
    usedMargin: 86,
    marketValue: ********,
    assetType: 1,
    frozenVolume: 0,
    frozenTodayVolume: 0,
    positionCost: 0,
    settlementPrice: 0,
    marginRateByMoney: 0,
    marginRateByVolume: 0,
    tradingDay: '2023-01-01',
  },
];

// 模拟委托数据
const mockOrders: TableOrderInfo[] = [
  {
    id: 1,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    instrument: '600036',
    instrumentName: '招商银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 3132,
    tradedVolume: 100,
    tradedPrice: 31.32,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 40,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.全成,
    localOrderId: 'L2023010100001',
    exchangeOrderId: 'E2023010100001',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 09:30:00',
    tradingDay: '2023-01-01',
    customId: 1,
    businessFlag: 0,
  },
  {
    id: 2,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    instrument: '600519',
    instrumentName: '贵州茅台',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 0,
    tradedVolume: 0,
    tradedPrice: 0,
    cancelledVolume: 100,
    volumeOriginal: 100,
    commission: 0,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.已撤,
    localOrderId: 'L2023010100002',
    exchangeOrderId: 'E2023010100002',
    orderPrice: 1800.0,
    orderPriceType: 1,
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 10:15:00',
    tradingDay: '2023-01-01',
    customId: 2,
    businessFlag: 0,
  },
  {
    id: 3,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    instrument: '601398',
    instrumentName: '工商银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 1566,
    tradedVolume: 50,
    tradedPrice: 31.32,
    cancelledVolume: 50,
    volumeOriginal: 100,
    commission: 20,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.废单,
    localOrderId: 'L2023010100003',
    exchangeOrderId: 'E2023010100003',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 11:05:00',
    tradingDay: '2023-01-01',
    customId: 3,
    businessFlag: 0,
  },
  {
    id: 4,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    instrument: '601288',
    instrumentName: '农业银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 100,
    tradedAmount: 0,
    tradedVolume: 0,
    tradedPrice: 0,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 0,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.未成交,
    localOrderId: 'L2023010100004',
    exchangeOrderId: 'E2023010100004',
    orderPrice: 3.5,
    orderPriceType: 1,
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 13:30:00',
    tradingDay: '2023-01-01',
    customId: 4,
    businessFlag: 0,
  },
  {
    id: 5,
    parentOrderId: 0,
    userId: 1,
    userName: 'admin',
    fundId: 1,
    fundName: '',
    accountId: ***********,
    financeAccount: 'n31806',
    accountName: '爱建测试1901',
    instrument: '601988',
    instrumentName: '中国银行',
    frozenMargin: 0,
    frozenCommission: 0,
    frozenVolume: 0,
    tradedAmount: 3132,
    tradedVolume: 100,
    tradedPrice: 31.32,
    cancelledVolume: 0,
    volumeOriginal: 100,
    commission: 40,
    foreign: false,
    assetType: 1,
    orderStatus: OrderStatusEnum.部分成交,
    localOrderId: 'L2023010100005',
    exchangeOrderId: 'E2023010100005',
    orderPrice: 31.32,
    orderPriceType: 1,
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    hedgeFlag: 1,
    forceClose: false,
    orderTime: '2023-01-01 14:20:00',
    tradingDay: '2023-01-01',
    customId: 5,
    businessFlag: 0,
  },
];

// 模拟成交数据
const mockTradeRecords: TableTradeRecordInfo[] = [
  {
    tradeId: 'T2023010100001',
    exchangeOrderId: 'E2023010100001',
    orderId: 1,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '600036',
    instrumentName: '招商银行',
    tradeTime: '2023-01-01 09:30:15',
    volume: 100,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010100002',
    exchangeOrderId: 'E2023010100003',
    orderId: 3,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '601398',
    instrumentName: '工商银行',
    tradeTime: '2023-01-01 11:05:30',
    volume: 50,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010100003',
    exchangeOrderId: 'E2023010100005',
    orderId: 5,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-01',
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    instrument: '601988',
    instrumentName: '中国银行',
    tradeTime: '2023-01-01 14:20:45',
    volume: 100,
    tradedPrice: 31.32,
  },
  {
    tradeId: 'T2023010200001',
    exchangeOrderId: 'E2023010200001',
    orderId: 6,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-02',
    direction: TradeDirectionEnum.买入,
    positionEffect: PositionEffectEnum.开仓,
    instrument: '601628',
    instrumentName: '中国人寿',
    tradeTime: '2023-01-02 09:45:10',
    volume: 200,
    tradedPrice: 42.56,
  },
  {
    tradeId: 'T2023010200002',
    exchangeOrderId: 'E2023010200002',
    orderId: 7,
    userId: 1,
    userName: 'admin',
    accountId: ***********,
    accountName: '爱建测试1901',
    fundId: 1,
    fundName: '',
    assetType: 1,
    tradingDay: '2023-01-02',
    direction: TradeDirectionEnum.卖出,
    positionEffect: PositionEffectEnum.平仓,
    instrument: '601318',
    instrumentName: '中国平安',
    tradeTime: '2023-01-02 14:30:25',
    volume: 150,
    tradedPrice: 56.78,
  },
];
// 模拟监控池股票数据
const mockPoolDetails: PoolDetail[] = [
  {
    id: 1,
    instrument: '600036',
    instrumentName: '招商银行',
    status: 2,
    risePercent: 0.04,
    poolName: '银行股票池',
    poolId: 1,
    position: 0.3,
  },
  {
    id: 2,
    instrument: '601398',
    instrumentName: '工商银行',
    status: 1,
    risePercent: 0.03,
    poolName: '银行股票池',
    poolId: 1,
    position: 0.2,
  },
  {
    id: 3,
    instrument: '601288',
    instrumentName: '农业银行',
    status: 0,
    risePercent: 0.02,
    poolName: '银行股票池',
    poolId: 1,
    position: 0.15,
  },
  {
    id: 4,
    instrument: '601988',
    instrumentName: '中国银行',
    status: 1,
    risePercent: -0.02,
    poolName: '银行股票池',
    poolId: 1,
    position: 0.25,
  },
  {
    id: 5,
    instrument: '601328',
    instrumentName: '交通银行',
    status: 0,
    risePercent: 0.01,
    poolName: '银行股票池',
    poolId: 1,
    position: 0.1,
  },
];

// 模拟股票行情数据
const mockTickData: Record<string, StandardTick> = {
  '600036': {
    askPrice: [10.81, 10.82, 10.83, 10.84, 10.85],
    askVolume: [2057, 1034, 4653, 3067, 2103],
    bidPrice: [10.8, 10.79, 10.78, 10.77, 10.76],
    bidVolume: [1045, 3058, 7805, 3058, 2103],
    exchange: 'SSE',
    highPrice: 10.85,
    instrumentID: '600036',
    lastPrice: 10.81,
    lowPrice: 10.71,
    upperLimitPrice: 11.88,
    lowerLimitPrice: 9.72,
    openPrice: 10.75,
    position: 0,
    preClosePrice: 10.75,
    settlePrice: 0,
    strTime: '10:23',
    turnover: **********,
    updateTime: '2023-01-01 10:23:00',
    volume: ********,
  },
  '601398': {
    askPrice: [10.75, 10.76, 10.77, 10.78, 10.79],
    askVolume: [836, 36, 3, 4, 7],
    bidPrice: [10.74, 10.73, 10.72, 10.71, 10.7],
    bidVolume: [21, 1025, 39, 39, 1025],
    exchange: 'SSE',
    highPrice: 10.79,
    instrumentID: '601398',
    lastPrice: 10.71,
    lowPrice: 10.7,
    upperLimitPrice: 11.81,
    lowerLimitPrice: 9.67,
    openPrice: 10.72,
    position: 0,
    preClosePrice: 10.74,
    settlePrice: 0,
    strTime: '10:23',
    turnover: 987654321,
    updateTime: '2023-01-01 10:23:00',
    volume: 9876543,
  },
  '601288': {
    askPrice: [11.72, 11.73, 11.74, 11.75, 11.76],
    askVolume: [234, 1034, 4653, 3067, 2103],
    bidPrice: [11.71, 11.7, 11.69, 11.68, 11.67],
    bidVolume: [1045, 3058, 7805, 3058, 2103],
    exchange: 'SSE',
    highPrice: 10.79,
    instrumentID: '601288',
    lastPrice: 11.74,
    lowPrice: 10.7,
    upperLimitPrice: 11.81,
    lowerLimitPrice: 9.67,
    openPrice: 10.72,
    position: 0,
    preClosePrice: 10.74,
    settlePrice: 0,
    strTime: '10:22',
    turnover: 555666777,
    updateTime: '2023-01-01 10:22:00',
    volume: 5556667,
  },
  '601988': {
    askPrice: [10.01, 10.02, 10.03, 10.04, 10.05],
    askVolume: [7805, 3058, 2103, 1045, 3058],
    bidPrice: [10.0, 9.99, 9.98, 9.97, 9.96],
    bidVolume: [3058, 2103, 1045, 3058, 7805],
    exchange: 'SSE',
    highPrice: 10.76,
    instrumentID: '601988',
    lastPrice: 10.01,
    lowPrice: 10.67,
    upperLimitPrice: 11.78,
    lowerLimitPrice: 9.64,
    openPrice: 10.69,
    position: 0,
    preClosePrice: 10.71,
    settlePrice: 0,
    strTime: '10:19',
    turnover: 333444555,
    updateTime: '2023-01-01 10:19:00',
    volume: 3334445,
  },
  '601328': {
    askPrice: [10.72, 10.73, 10.74, 10.75, 10.76],
    askVolume: [3058, 2103, 1045, 3058, 7805],
    bidPrice: [10.71, 10.7, 10.69, 10.68, 10.67],
    bidVolume: [7805, 3058, 2103, 1045, 3058],
    exchange: 'SSE',
    highPrice: 10.76,
    instrumentID: '601328',
    lastPrice: 10.88,
    lowPrice: 10.67,
    upperLimitPrice: 11.78,
    lowerLimitPrice: 9.64,
    openPrice: 10.69,
    position: 0,
    preClosePrice: 10.71,
    settlePrice: 0,
    strTime: '10:18',
    turnover: 111222333,
    updateTime: '2023-01-01 10:18:00',
    volume: 1112223,
  },
};

// 生成实时成交数据的函数
const generateRealtimeTrades = (instrument: string): RealtimeTrade[] => {
  const basePrice = mockTickData[instrument]?.lastPrice || 10.0;
  const trades: RealtimeTrade[] = [];

  for (let i = 0; i < 20; i++) {
    const time = new Date();
    time.setMinutes(time.getMinutes() - i);
    time.setSeconds(time.getSeconds() - Math.floor(Math.random() * 60));
    const timeStr = `${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`;

    const priceVariation = (Math.random() - 0.5) * 0.1; // ±0.05的价格波动
    const price = Number((basePrice + priceVariation).toFixed(2));
    const volume = Math.floor(Math.random() * 1000) + 100;
    const direction = Math.random() > 0.5 ? 'B' : 'S';

    trades.push({
      time: timeStr,
      price,
      volume,
      direction,
    });
  }

  return trades.sort((a, b) => b.time.localeCompare(a.time)); // 按时间倒序排列
};

// 模拟策略日志数据
const mockStrategyLogs: StrategyLog[] = [
  {
    triggerTime: '09:30:33',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '云南白药',
    instrument: '002963',
    amount: 30000,
  },
  {
    triggerTime: '09:31:15',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '招商银行',
    instrument: '600036',
    amount: 150000,
  },
  {
    triggerTime: '09:32:08',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '工商银行',
    instrument: '601398',
    amount: 200000,
  },
  {
    triggerTime: '09:33:42',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '农业银行',
    instrument: '601288',
    amount: 80000,
  },
  {
    triggerTime: '09:35:20',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '中国银行',
    instrument: '601988',
    amount: 120000,
  },
  {
    triggerTime: '09:36:55',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '交通银行',
    instrument: '601328',
    amount: 60000,
  },
  {
    triggerTime: '09:40:30',
    triggerType: '扫1',
    triggerContent: '压单金额3000万少于设定5000万',
    instrumentName: '招商银行',
    instrument: '600036',
    amount: 180000,
  },
];

// 模拟股票池列表
const mockStrategyPools: Pool[] = [
  {
    id: 1,
    groupName: '银行股票池',
    status: 0,
    groupType: 1,
    size: 5,
    positionRate: 100,
    strategyConfig: {
      limitTriggerNum: 3,
      shConfig: [
        { firstAmount: 5000, secondAmount: 3000, positionEffect: 30 },
        { firstAmount: 3000, secondAmount: 2000, positionEffect: 20 },
        { firstAmount: 2000, secondAmount: 1000, positionEffect: 10 },
      ],
      szConfig: [
        { firstAmount: 5000, secondAmount: 3000, positionEffect: 30 },
        { firstAmount: 3000, secondAmount: 2000, positionEffect: 20 },
        { firstAmount: 2000, secondAmount: 1000, positionEffect: 10 },
      ],
    },
    cancelConfig: [],
    effectTime: {
      am: { begin: '09:30:00', end: '11:30:00' },
      pm: { begin: '13:00:00', end: '15:00:00' },
    },
    userId: 1,
    userName: 'admin',
  },
];

export default defineMock([
  {
    url: '/api/login',
    method: 'POST',
    body: req => {
      return {
        errorCode: 0,
        data: {
          token: '**********',
          username: req.body.username,
          role: 'admin',
        },
      };
    },
  },
  {
    url: '/api/accounts',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockAccounts,
      };
    },
  },
  {
    url: '/api/positions',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        data: mockPositions,
      };
    },
  },
  {
    url: '/api/orders',
    method: 'GET',
    body: req => {
      console.log(req.query);
      // 从mockOrders随机选择订单并生成指定count数量的数据
      const generateMockOrders = (count: number) => {
        return Array.from({ length: count }, () => {
          return {
            ...mockOrders[Math.floor(Math.random() * mockOrders.length)],
            id: Math.random(),
            orderPrice: Number((Math.random() * 100).toFixed(2)),
            orderTime: (() => {
              const date = new Date();
              date.setHours(Math.floor(Math.random() * 24));
              date.setMinutes(Math.floor(Math.random() * 60));
              date.setSeconds(Math.floor(Math.random() * 60));
              return date.toISOString().replace('T', ' ').slice(0, 19);
            })(),
          };
        });
      };
      return {
        errorCode: 0,
        data: generateMockOrders(100),
      };
    },
  },
  {
    url: '/api/trades',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        data: mockTradeRecords,
      };
    },
  },
  {
    url: '/api/strategy/detail/all',
    method: 'GET',
    body: req => {
      console.log(req.params);
      return {
        errorCode: 0,
        data: mockPoolDetails,
      };
    },
  },
  {
    url: '/api/strategy/detail/status',
    method: 'PUT',
    body: req => {
      console.log(req.body);
      return {
        errorCode: 0,
      };
    },
  },
  {
    url: '/api/strategy/detail',
    method: 'POST',
    body: req => {
      console.log(req.body);
      return {
        errorCode: 0,
        data: req.body,
      };
    },
  },
  {
    url: '/api/strategy/detail',
    method: 'DELETE',
    body: req => {
      console.log(req.body);
      return {
        errorCode: 0,
      };
    },
  },
  {
    url: '/api/tick',
    method: 'GET',
    body: req => {
      const instrument = req.query.instrument as string;
      const tickData = mockTickData[instrument];
      if (tickData) {
        return {
          errorCode: 0,
          data: tickData,
        };
      } else {
        return {
          errorCode: 1,
          msg: '未找到该股票的行情数据',
        };
      }
    },
  },
  {
    url: '/api/realtime-trades',
    method: 'GET',
    body: req => {
      const instrument = req.query.instrument as string;
      if (mockTickData[instrument]) {
        return {
          errorCode: 0,
          data: generateRealtimeTrades(instrument),
        };
      } else {
        return {
          errorCode: 1,
          msg: '未找到该股票的成交数据',
        };
      }
    },
  },
  {
    url: '/api/strategy/logs',
    method: 'GET',
    body: req => {
      const instrument = req.query.instrument as string;
      if (instrument) {
        console.log(instrument);
        // 根据股票代码过滤策略日志
        const filteredLogs = mockStrategyLogs.filter(log => log.instrument.includes(instrument));
        return {
          errorCode: 0,
          data: filteredLogs,
        };
      } else {
        return {
          errorCode: 0,
          data: mockStrategyLogs,
        };
      }
    },
  },
  {
    url: '/api/strategy',
    method: 'GET',
    body: () => {
      return {
        errorCode: 0,
        data: mockStrategyPools,
      };
    },
  },
  {
    url: '/api/strategy/start',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        msg: '',
      };
    },
  },
  {
    url: '/api/strategy/stop',
    method: 'GET',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        msg: '策略池停止成功',
      };
    },
  },
  {
    url: '/api/strategy',
    method: 'DELETE',
    body: req => {
      console.log(req.query);
      return {
        errorCode: 0,
        msg: '',
      };
    },
  },
]);
