import { FunctionCode, FunctionCodeMap } from '@/enum';
import type { FunctionCodeBody } from '@/types/code';

/**
 * WebSocket 连接状态枚举
 */
enum ConnectionStatus {
  断连 = 'disconnected', // 连接已断开
  连接中 = 'connecting', // 正在建立连接
  已连接 = 'connected', // 连接已建立
  重连中 = 'reconnecting', // 正在尝试重新连接
}
/**
 * WebSocket 消息接口
 * @template T 消息体类型
 */
interface WsMessage<T extends FunctionCode> {
  fc: T;
  reqId: number;
  dataType: number;
  body?: FunctionCodeBody[T];
}

type HandlersMap<T extends FunctionCode> = Map<T, (data: FunctionCodeBody[T]) => void>;

/**
 * WebSocket 服务类
 * 实现 WebSocket 连接管理、消息收发、心跳检测和自动重连功能
 */
export class WebSocketService {
  private static instance: WebSocketService; // 单例实例
  private socket: WebSocket | null = null; // WebSocket 实例
  private reconnectAttempts = 0; // 当前重连尝试次数
  private readonly maxReconnectAttempts = 5; // 最大重连尝试次数
  private readonly reconnectDelayBase = 1000; // 重连基础延迟(毫秒)
  private heartbeatInterval?: number; // 心跳定时器ID
  private readonly heartbeatTime = 30000; // 心跳间隔时间(毫秒)
  private readonly headerLen = 20; // 消息头长度(字节)
  private isManualDisconnect = false; // 是否手动断开连接标志
  private messageHandlers: HandlersMap<any> = new Map(); // 消息处理器映射
  private status: ConnectionStatus = ConnectionStatus.断连; // 当前连接状态

  private constructor() {}

  /**
   * 获取 WebSocketService 单例实例
   * @returns WebSocketService 实例
   */
  static getInstance(): WebSocketService {
    if (!WebSocketService.instance) {
      WebSocketService.instance = new WebSocketService();
    }
    return WebSocketService.instance;
  }

  /**
   * 连接到 WebSocket 服务器
   * @param url WebSocket 服务器地址
   * @param callback 连接成功后的回调函数
   */
  connect(url: string, callback?: () => void): void {
    if (this.socket) {
      this.disconnect();
    }

    this.socket = new WebSocket(url);

    this.status = ConnectionStatus.连接中;
    this.socket.onopen = () => {
      this.status = ConnectionStatus.已连接;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
      callback?.();
      console.log('WebSocket connected');
    };

    this.socket.onmessage = async event => {
      const msg = await this.decode(event.data);
      this.handleMessage(msg);
    };

    this.socket.onclose = () => {
      console.log('WebSocket disconnected');

      this.status =
        this.reconnectAttempts < this.maxReconnectAttempts && !this.isManualDisconnect
          ? ConnectionStatus.重连中
          : ConnectionStatus.断连;
      this.stopHeartbeat();
      if (!this.isManualDisconnect) {
        this.attemptReconnect(url);
      }
      this.isManualDisconnect = false;
    };

    this.socket.onerror = error => {
      console.error('WebSocket error:', error);
    };
  }

  /**
   * 注册消息处理器
   * @param functionCode 消息类型
   * @param handler 处理函数
   */
  private registerHandler<T extends FunctionCode>(
    functionCode: T,
    handler: (data: FunctionCodeBody[T]) => void,
  ): void {
    this.messageHandlers.set(functionCode, handler);
  }

  /**
   * 移除消息处理器
   * @param functionCode 消息类型
   */
  private unregisterHandler(functionCode: FunctionCode): void {
    this.messageHandlers.delete(functionCode);
  }

  /**
   * 处理收到的 WebSocket 消息
   * @param message 收到的消息对象
   */
  private handleMessage<T extends FunctionCode>(message: WsMessage<T>): void {
    console.log('receive message:', message);
    const handler = this.messageHandlers.get(message.fc);
    if (handler) {
      handler(message.body);
    } else {
      console.warn(`No handler registered for function code ${message.fc}`);
    }
  }

  /**
   * 启动心跳检测
   * 每隔固定时间发送心跳消息以保持连接
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = window.setInterval(() => {
      this.send(FunctionCode.心跳, '', 0);
    }, this.heartbeatTime);
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }

  /**
   * 尝试重新连接
   * 使用指数退避算法计算重连延迟
   * @param url WebSocket 服务器地址
   */
  private attemptReconnect(url: string): void {
    console.log('reconnecting...');
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      const delay = this.reconnectDelayBase * Math.pow(2, this.reconnectAttempts);
      setTimeout(() => {
        this.reconnectAttempts++;
        this.connect(url);
      }, delay);
    }
  }

  /**
   * 发送 WebSocket 消息
   * @param fc 消息类型
   * @param body 消息体
   * @param dataType 数据类型，默认为1
   * @throws 当连接未建立时抛出错误
   */
  private send(fc: FunctionCode, body?: any, dataType = 1): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      const message = { fc, reqId: Date.now(), dataType, body };
      console.log('send message:', JSON.stringify(message));
      this.socket.send(this.encode(message));
    } else {
      console.error('WebSocket is not connected');
    }
  }

  /**
   * 编码消息为 ArrayBuffer
   * 消息格式: [fc(4B)][保留(4B)][reqId(4B)][dataType(4B)][bodyLen(4B)][body(nB)]
   * @param message 要编码的消息对象
   * @returns 编码后的 ArrayBuffer
   */
  private encode(message: WsMessage<any>): ArrayBuffer {
    if (message.body === null || message.body === undefined) {
      message.body = '';
    }
    if (typeof message.body !== 'string' && !(message.body instanceof ArrayBuffer)) {
      message.body = JSON.stringify(message.body);
    }

    const { fc, reqId, dataType, body } = message;
    const encoder = new TextEncoder();
    const bodyBytes = typeof body === 'string' ? encoder.encode(body).byteLength : 0; // 假设非字符串body已经处理过

    const buffer = new ArrayBuffer(this.headerLen + bodyBytes);
    const view = new DataView(buffer);

    // 写入大端序32位整数
    view.setInt32(0, fc, false); // fc
    view.setInt32(4, 0, false); // 高位保留
    view.setInt32(8, reqId & 0xffff, false); // reqId
    view.setInt32(12, dataType, false); // dataType
    view.setInt32(16, bodyBytes, false); // body长度

    if (typeof body === 'string') {
      const bodyView = new Uint8Array(buffer, this.headerLen);
      encoder.encodeInto(body, bodyView);
    } else if (body instanceof ArrayBuffer) {
      const bodyView = new Uint8Array(buffer, this.headerLen);
      bodyView.set(new Uint8Array(body));
    }

    return buffer;
  }

  /**
   * 解码 Blob 数据为消息对象
   * @param data 收到的 Blob 数据
   * @returns 解析后的消息对象 Promise
   */
  private decode(data: Blob): Promise<WsMessage<FunctionCode>> {
    return new Promise((resolve, reject) => {
      data
        .arrayBuffer()
        .then(buffer => {
          const view = new DataView(buffer);
          const fc: FunctionCode = view.getInt32(0, false);
          const reqId = view.getInt32(8, false);
          const dataType = view.getInt32(12, false);
          const bodyLength = view.getInt32(16, false);

          let body: any = '';
          if (bodyLength > 0) {
            const bodyBuffer = buffer.slice(this.headerLen, this.headerLen + bodyLength);
            try {
              const decoder = new TextDecoder();
              body = decoder.decode(bodyBuffer);
              // 尝试解析JSON
              body = JSON.parse(body);
            } catch {
              // 如果解析失败，保持原始ArrayBuffer
              body = bodyBuffer;
            }
          }

          resolve({
            fc,
            reqId,
            dataType,
            body,
          } as WsMessage<FunctionCode>);
        })
        .catch(reject);
    });
  }

  /**
   * 断开 WebSocket 连接
   * 设置手动断开标志以避免自动重连
   */
  private disconnect(): void {
    if (this.socket) {
      this.isManualDisconnect = true;
      this.status = ConnectionStatus.断连;
      this.stopHeartbeat();
      this.socket.close();
      this.socket = null;
    }
  }

  register<T extends FunctionCode>(fc: T, body?: FunctionCodeBody[T], datatype?: number) {
    const mapFc = FunctionCodeMap[fc];

    return new Promise((resolve, reject) => {
      // 资源清理函数
      const cleanup = () => this.unregisterHandler(mapFc);

      this.registerHandler(mapFc, data => {
        cleanup();
        resolve(data);
      });

      try {
        this.send(fc, body, datatype);
      } catch (err) {
        cleanup();
        reject(err);
      }
    });
  }
}

export const ws = WebSocketService.getInstance();
